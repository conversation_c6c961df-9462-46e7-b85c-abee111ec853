import { requireRole } from '@/lib/auth/server-auth';
import { getManagerGyms } from '@/lib/actions/dashboard/company/dashboard-actions';

import { ManagersClient } from './components/ManagersClient';
import { getCompanyInvitations } from '@/lib/actions/dashboard/company/gym-manager-invitation-actions';

export const dynamic = 'force-dynamic';

export default async function ManagersPage() {
  await requireRole('company_manager');

  // Paralel olarak gym'leri ve davet kodlarını getir
  const [gymsResult, invitationsResult] = await Promise.all([
    getManagerGyms(),
    getCompanyInvitations(),
  ]);

  if (!gymsResult.success) {
    throw new Error(gymsResult.error || 'Salonlar yüklenemedi');
  }

  if (!invitationsResult.success) {
    throw new Error(invitationsResult.error || 'Davet kodları yüklenemedi');
  }

  return (
    <ManagersClient 
      gyms={gymsResult.data || []} 
      invitations={invitationsResult.data || []}
    />
  );
}
