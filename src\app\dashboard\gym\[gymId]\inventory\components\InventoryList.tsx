'use client';

import { useState } from 'react';
import {
  MoreH<PERSON>zontal,
  Edit,
  Trash2,
  Plus,
  Minus,
  Package,
  AlertTriangle,
  MapPin,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  GymInventory, 
  InventoryCategory,
  INVENTORY_UNIT_TYPES 
} from '@/types/database/equipment-inventory';
import { deleteInventory } from '@/lib/actions/dashboard/company/inventory-actions';
import { toast } from 'sonner';

interface InventoryListProps {
  inventory: GymInventory[];
  categories: InventoryCategory[];
  onCreateInventory?: () => void;
  onEditInventory?: (inventory: GymInventory) => void;
  onStockAdjustment?: (inventory: GymInventory, type: 'add' | 'remove') => void;
}

export function InventoryList({
  inventory,
  categories,
  onCreateInventory,
  onEditInventory,
  onStockAdjustment
}: InventoryListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<GymInventory | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const getUnitTypeLabel = (unitType: string) => {
    return INVENTORY_UNIT_TYPES.find(u => u.value === unitType)?.label || unitType;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };



  // Depo durumu hesaplama fonksiyonları
  const getInventoryStatus = (item: GymInventory) => {
    const statuses = [];

    // Stok durumu kontrolü
    if (item.current_stock === 0) {
      statuses.push({
        type: 'stock_out',
        message: 'Tükendi',
        color: 'red',
        icon: AlertCircle,
        priority: 1
      });
    } else if (item.is_low_stock) {
      statuses.push({
        type: 'low_stock',
        message: 'Düşük Stok',
        color: 'yellow',
        icon: AlertTriangle,
        priority: 2
      });
    }

    // Son kullanma tarihi kontrolü
    if (item.expiry_date) {
      const expiry = new Date(item.expiry_date);
      const today = new Date();
      const diffTime = expiry.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        statuses.push({
          type: 'expired',
          message: `${Math.abs(diffDays)} gün geçmiş`,
          color: 'red',
          icon: AlertCircle,
          priority: 1
        });
      } else if (diffDays <= 7) {
        statuses.push({
          type: 'expiring_soon',
          message: diffDays === 0 ? 'Bugün sona eriyor' : `${diffDays} gün kaldı`,
          color: 'red',
          icon: Clock,
          priority: 1
        });
      } else if (diffDays <= 30) {
        statuses.push({
          type: 'expiring_warning',
          message: `${diffDays} gün kaldı`,
          color: 'yellow',
          icon: Clock,
          priority: 2
        });
      }
    }

    // Eğer hiç sorun yoksa
    if (statuses.length === 0) {
      statuses.push({
        type: 'good',
        message: 'Normal',
        color: 'green',
        icon: CheckCircle,
        priority: 3
      });
    }

    // Önceliğe göre sırala (düşük sayı = yüksek öncelik)
    return statuses.sort((a, b) => a.priority - b.priority);
  };

  const handleDeleteClick = (item: GymInventory) => {
    setItemToDelete(item);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!itemToDelete) return;

    setIsDeleting(true);
    try {
      const result = await deleteInventory(itemToDelete.id, itemToDelete.gym_id);
      if (result.success) {
        toast.success('Ürün başarıyla silindi');
        // Refresh the page to update the list
      } else {
        toast.error(result.error || 'Ürün silinirken hata oluştu');
      }
    } catch (error) {
      toast.error('Beklenmeyen bir hata oluştu');
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setItemToDelete(null);
    }
  };

  if (inventory.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Package className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Henüz ürün yok</h3>
          <p className="text-muted-foreground text-center mb-4">
            İlk ürününüzü ekleyerek başlayın
          </p>
          <Button onClick={onCreateInventory}>Ürün Ekle</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {inventory.map((item) => {
          const category = categories.find(c => c.id === item.category_id);
          const inventoryStatuses = getInventoryStatus(item);

          return (
            <Card key={item.id} className="group hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold truncate">{item.name}</h3>
                    {item.sku && (
                      <p className="text-sm text-muted-foreground">
                        SKU: {item.sku}
                      </p>
                    )}
                    <div className="flex flex-wrap gap-1 mt-1">
                      {category && (
                        <Badge variant="outline" className="text-xs">
                          {category.name}
                        </Badge>
                      )}

                      {/* Durum Badge'leri */}
                      {inventoryStatuses.slice(0, 2).map((status, index) => {
                        const Icon = status.icon;
                        return (
                          <Badge
                            key={index}
                            variant={
                              status.color === 'red' ? 'destructive' :
                              status.color === 'yellow' ? 'outline' :
                              'outline'
                            }
                            className={`text-xs flex items-center gap-1 ${
                              status.color === 'yellow' ? 'text-amber-600 border-amber-300 bg-amber-50 dark:bg-amber-900/30' :
                              status.color === 'green' ? 'text-green-600 border-green-300 bg-green-50 dark:bg-green-900/30' :
                              ''
                            }`}
                          >
                            <Icon className="h-3 w-3" />
                            {status.message}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEditInventory?.(item)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Düzenle
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onStockAdjustment?.(item, 'add')}>
                        <Plus className="h-4 w-4 mr-2" />
                        Stok Ekle
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onStockAdjustment?.(item, 'remove')}>
                        <Minus className="h-4 w-4 mr-2" />
                        Stok Çıkar
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(item)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Sil
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Stock Status */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Stok:</span>
                  <span className="font-medium">
                    {item.current_stock} {getUnitTypeLabel(item.unit_type)}
                  </span>
                </div>

                {/* Minimum Stock */}
                {item.minimum_stock > 0 && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Min. Stok:</span>
                    <span>{item.minimum_stock} {getUnitTypeLabel(item.unit_type)}</span>
                  </div>
                )}

                {/* Location */}
                {item.location && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span className="truncate">{item.location}</span>
                  </div>
                )}

                {/* Pricing */}
                <div className="space-y-1 text-sm">
                  {item.unit_cost && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Maliyet:</span>
                      <span>{formatCurrency(item.unit_cost)}</span>
                    </div>
                  )}
                  {item.selling_price && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Satış:</span>
                      <span>{formatCurrency(item.selling_price)}</span>
                    </div>
                  )}
                </div>

                {/* Expiry Date */}
                {item.expiry_date && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Son Kullanma:</span>
                    <span>{formatDate(item.expiry_date)}</span>
                  </div>
                )}

                {/* Supplier */}
                {item.supplier_name && (
                  <div className="text-xs text-muted-foreground">
                    Tedarikçi: {item.supplier_name}
                  </div>
                )}

                {/* Total Value */}
                {item.unit_cost && (
                  <div className="pt-2 border-t">
                    <div className="flex justify-between text-sm font-medium">
                      <span>Toplam Değer:</span>
                      <span>{formatCurrency(item.current_stock * item.unit_cost)}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Ürünü Sil</AlertDialogTitle>
            <AlertDialogDescription>
              &quot;{itemToDelete?.name}&quot; ürününü silmek istediğinizden emin misiniz? 
              Bu işlem geri alınamaz ve tüm işlem kayıtları da silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Siliniyor...' : 'Sil'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
