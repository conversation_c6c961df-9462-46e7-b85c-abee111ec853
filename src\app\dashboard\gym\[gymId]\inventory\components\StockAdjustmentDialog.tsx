'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Plus, Minus } from 'lucide-react';
import { 
  GymInventory,
  INVENTORY_TRANSACTION_TYPES 
} from '@/types/database/equipment-inventory';
import { createInventoryTransaction } from '@/lib/actions/dashboard/company/inventory-actions';
import { toast } from 'sonner';

const stockAdjustmentSchema = z.object({
  transaction_type: z.enum(['purchase', 'sale', 'adjustment', 'return', 'damage']),
  quantity: z.number().min(1, 'Miktar 1 veya daha büyük olmalıdır'),
  unit_price: z.number().min(0).optional(),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
});

type FormData = z.infer<typeof stockAdjustmentSchema>;

interface StockAdjustmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  inventory: GymInventory | null;
  adjustmentType: 'add' | 'remove';
}

export function StockAdjustmentDialog({
  open,
  onOpenChange,
  inventory,
  adjustmentType,
}: StockAdjustmentDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const params = useParams();
  const gymId = params.gymId as string;

  const form = useForm<FormData>({
    resolver: zodResolver(stockAdjustmentSchema),
    defaultValues: {
      transaction_type: 'adjustment',
      quantity: 1,
      unit_price: undefined,
      reference_number: '',
      notes: '',
    },
  });

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      const defaultType = adjustmentType === 'add' ? 'purchase' : 'sale';
      form.reset({
        transaction_type: defaultType,
        quantity: 1,
        unit_price: inventory?.unit_cost || undefined,
        reference_number: '',
        notes: '',
      });
    }
  }, [open, adjustmentType, inventory, form]);

  const onSubmit = async (data: FormData) => {
    if (!inventory) return;

    setIsSubmitting(true);
    try {
      // Calculate final quantity based on adjustment type
      const finalQuantity = adjustmentType === 'remove' ? -Math.abs(data.quantity) : Math.abs(data.quantity);

      const cleanData = {
        inventory_id: inventory.id,
        transaction_type: data.transaction_type,
        quantity: finalQuantity,
        unit_cost: data.unit_price,
        reference_number: data.reference_number || undefined,
        notes: data.notes || undefined,
        transaction_date: new Date().toISOString().split('T')[0],
      };

      const result = await createInventoryTransaction(cleanData, gymId);
      
      if (result.success) {
        const action = adjustmentType === 'add' ? 'eklendi' : 'çıkarıldı';
        toast.success(`Stok başarıyla ${action}`);
        onOpenChange(false);
      } else {
        toast.error(result.error || 'Stok işlemi sırasında hata oluştu');
      }
    } catch (error) {
      toast.error('Beklenmeyen bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDialogTitle = () => {
    return adjustmentType === 'add' ? 'Stok Ekle' : 'Stok Çıkar';
  };

  const getDialogDescription = () => {
    const action = adjustmentType === 'add' ? 'ekleyin' : 'çıkarın';
    return `${inventory?.name} için stok ${action}`;
  };

  const getTransactionTypes = () => {
    if (adjustmentType === 'add') {
      return INVENTORY_TRANSACTION_TYPES.filter(type => 
        ['purchase', 'adjustment', 'return'].includes(type.value)
      );
    } else {
      return INVENTORY_TRANSACTION_TYPES.filter(type => 
        ['sale', 'adjustment', 'damage'].includes(type.value)
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {adjustmentType === 'add' ? (
              <Plus className="h-5 w-5 text-green-600" />
            ) : (
              <Minus className="h-5 w-5 text-red-600" />
            )}
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>
            {getDialogDescription()}
          </DialogDescription>
        </DialogHeader>

        {inventory && (
          <div className="bg-muted/50 p-3 rounded-lg space-y-2">
            <div className="flex justify-between items-center">
              <span className="font-medium">{inventory.name}</span>
              <Badge variant="outline">{inventory.unit_type}</Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              Mevcut Stok: <span className="font-medium">{inventory.current_stock}</span>
            </div>
            {inventory.unit_cost && (
              <div className="text-sm text-muted-foreground">
                Birim Maliyet: <span className="font-medium">₺{inventory.unit_cost.toFixed(2)}</span>
              </div>
            )}
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Transaction Type */}
            <FormField
              control={form.control}
              name="transaction_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>İşlem Türü *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {getTransactionTypes().map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Quantity */}
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Miktar *</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      min="1"
                      placeholder="1" 
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Unit Price */}
            <FormField
              control={form.control}
              name="unit_price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Birim Fiyat (₺)</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.01"
                      min="0"
                      placeholder="0.00" 
                      {...field}
                      onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Reference Number */}
            <FormField
              control={form.control}
              name="reference_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Referans Numarası</FormLabel>
                  <FormControl>
                    <Input placeholder="Fatura/Fiş numarası" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notlar</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="İşlem hakkında ek bilgiler..."
                      className="min-h-[60px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                İptal
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
                variant={adjustmentType === 'add' ? 'default' : 'destructive'}
              >
                {isSubmitting ? 'İşleniyor...' : getDialogTitle()}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
