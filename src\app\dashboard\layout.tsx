import { Metadata } from 'next';
import { DashboardHeader } from '@/components/header/dashboard-header';
import { getDashboardContext } from '@/lib/actions/auth/header-auth-actions';
import { DashboardProvider } from '@/context/dashboard-context';

export const metadata: Metadata = {
  title: 'Dashboard | Sportiva',
  description:
    'Sportiva kullanıcı paneli. Üyeliklerinizi yönetin, salonlarınızı takip edin.',
  robots: {
    index: false,
    follow: false,
  },
};

export const dynamic = 'force-dynamic';

export default async function DashboardLayout({ children }: { children: React.ReactNode }) {
  const dashboardContext = await getDashboardContext();

  return (
    <DashboardProvider value={dashboardContext}>
      <main
        id="main-content"
        className="relative flex min-h-screen w-full flex-col overflow-hidden"
      >
        <DashboardHeader />
        <div className="h-[calc(100vh-4rem)] overflow-y-auto px-4 py-8 md:pr-10 md:pl-24">
          {children}
        </div>
      </main>
    </DashboardProvider>
  );
}
