import { redirect } from 'next/navigation';
import { getAuthenticatedUser, getUserRoles, checkManagerActiveSubscription } from '@/lib/auth/server-auth';
import { OnboardingClient } from './components/OnboardingClient';
import { getProfile } from '@/lib/actions/user/profile-actions';
import { PlatformRoles } from '@/types/database/enums';
import { getAllPlatformPackages } from '@/lib/actions/business/platform-packages';

export const dynamic = 'force-dynamic';

export default async function OnboardingPage({
  searchParams,
}: {
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  const roles = await getUserRoles();
  const user = await getAuthenticatedUser();
  const params = await searchParams;

  // Kullanıcının tüm rollere sahip olup olmadığını kontrol et
  const allRoles: PlatformRoles[] = [
    'member',
    'trainer',
    'company_manager',
    'gym_manager',
  ];
  const userRoles: PlatformRoles[] = [];

  if (roles.includes('member')) userRoles.push('member');
  if (roles.includes('trainer')) userRoles.push('trainer');
  if (roles.includes('company_manager')) userRoles.push('company_manager');
  if (roles.includes('gym_manager')) userRoles.push('gym_manager');


  // Eğer kullanıcı manager ve aboneliği aktif değilse, ödeme adımını zorunlu kıl
  const forcePayment =
    !!user &&
    userRoles.includes('company_manager') &&
    !(await checkManagerActiveSubscription());

  const stepParam = typeof params?.step === 'string' ? params?.step : undefined;

  // Tüm rollere sahip ama ödeme tamamlanmamışsa veya step=payment ise panel'e değil onboarding'e kal
  if (userRoles.length === allRoles.length && !forcePayment && stepParam !== 'payment') {
    redirect('/dashboard');
  }

  // Kullanıcının profil bilgilerini al
  const profileResult = await getProfile();
  const userProfile =
    profileResult.success && profileResult.data ? profileResult.data : null;

  const packagesResult = await getAllPlatformPackages();
  const packages = packagesResult.success && packagesResult.data ? packagesResult.data : [];

  // Client component'e gerekli verileri props olarak geç
  return (
    <OnboardingClient currentRoles={userRoles} userProfile={userProfile} packages={packages} />
  );
}
