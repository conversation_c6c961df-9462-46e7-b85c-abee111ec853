'use client';

import { Suspense } from 'react';
import { UserDropdown } from './user-dropdown';
import { EnhancedBreadcrumbs } from '../dashboard/shared/enhanced-breadcrumbs';
import { NotificationsButton } from './notifications-button';
import { DashboardMobileNavigation } from './mobile/dashboard-mobile-navigation';
import { SportivaLogo } from './shared/sportiva-logo';
import { useDashboard } from '@/context/dashboard-context';

// Dynamic auth-dependent content
function DashboardHeaderDynamic() {
  const { profile } = useDashboard();

  return (
    <>
      <div className="flex items-center space-x-3 md:space-x-4">
        <DashboardMobileNavigation />
        <SportivaLogo />
        <EnhancedBreadcrumbs />
      </div>
      <div className="ml-auto flex items-center space-x-3">
        {profile && <NotificationsButton userId={profile.id} />}
        {profile && <UserDropdown profile={profile} />}
      </div>
    </>
  );
}

// Fallback for loading state
function DashboardHeaderFallback() {
  return (
    <>
      <div className="flex items-center space-x-2 md:space-x-4">
        {/* Mobile navigation skeleton */}
        <div className="md:hidden">
          <div className="bg-muted/60 h-9 w-9 animate-pulse rounded-md" />
        </div>
        <SportivaLogo />
        {/* Enhanced breadcrumbs skeleton - more realistic */}
        <div className="hidden items-center space-x-2 md:flex">
          {/* Role selector skeleton */}
          <div className="flex h-14 items-center">
            <div className="bg-muted/60 h-8 w-20 animate-pulse rounded-l-md" />
            <div className="bg-muted/40 border-border/50 h-8 w-6 animate-pulse rounded-r-md border-l" />
          </div>
          <div className="text-muted-foreground/50 text-sm">/</div>
          {/* Gym selector skeleton */}
          <div className="bg-muted/60 h-8 w-32 animate-pulse rounded-md" />
          <div className="text-muted-foreground/50 text-sm">/</div>
          {/* Current page skeleton */}
          <div className="bg-muted/60 h-6 w-24 animate-pulse rounded" />
        </div>
      </div>
      <div className="ml-auto flex items-center space-x-3">
        {/* Notifications skeleton - more realistic */}
        <div className="relative">
          <div className="bg-muted/60 h-9 w-9 animate-pulse rounded-full" />
          <div className="bg-primary/60 absolute -top-1 -right-1 h-4 w-4 animate-pulse rounded-full" />
        </div>
        {/* User dropdown skeleton - more realistic */}
        <div className="flex items-center space-x-2">
          <div className="bg-muted/60 ring-muted/30 h-9 w-9 animate-pulse rounded-full ring-2" />
        </div>
      </div>
    </>
  );
}

export function DashboardHeader() {
  return (
    <header
      className="bg-background/95 supports-[backdrop-filter]:bg-background/60 w-full border-b backdrop-blur"
      role="banner"
    >
      <div className="flex h-14 w-full items-center px-4 md:px-8">
        <Suspense fallback={<DashboardHeaderFallback />}>
          <DashboardHeaderDynamic />
        </Suspense>
      </div>
    </header>
  );
}
