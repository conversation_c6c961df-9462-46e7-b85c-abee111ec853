'use server';

import { getAuthenticatedUser, getUserRoles } from '@/lib/auth/server-auth';
import { getProfile } from '@/lib/actions/user/profile-actions';
import { getManagerGyms } from '@/lib/actions/dashboard/company/dashboard-actions';
import { getTrainerGyms } from '@/lib/actions/user/trainer-actions';
import { getManagedGymsByManager } from '@/lib/actions/user/gym-manager-actions';
import { HeaderAuthStatus, RoleOption, GymData } from '@/components/header/shared/header-types';
import { ROLE_OPTIONS } from '@/components/header/shared/header-constants';
import { checkGymAccess } from '@/lib/actions/gym/gym-access-control';
import type { GymAccessControlResult } from '@/lib/types/gym-access-control';


export const getDashboardContext = async (
  gymId?: string,
): Promise<HeaderAuthStatus & { gymAccess?: GymAccessControlResult }> => {
  try {
    // Auth kontrolü yap
    const user = await getAuthenticatedUser();

    if (!user) {
      return {
        authenticated: false,
        profile: null,
        roleOptions: [],
        companyGyms: [],
        trainerGyms: [],
        gymManagerGyms: [],
        isManager: false,
        isTrainer: false,
        isMember: false,
        ...(gymId ? { gymAccess: { hasAccess: false, role: 'none', gymId } } : {}),
      };
    }

    // Kullanıcı varsa profil ve rol bilgilerini çek
    const [profileResult, roles] = await Promise.all([
      getProfile(),
      getUserRoles(),
    ]);

    // Rol seçeneklerini oluştur
    const roleOptions = roles
      .map(role => {
        if (ROLE_OPTIONS[role]) {
          return {
            ...ROLE_OPTIONS[role],
            available: true,
          };
        }
        return null;
      })
      .filter(Boolean) as RoleOption[];

    // Manager ise gym bilgilerini çek
    let companyGyms: any[] = [];
    if (roles.includes('company_manager')) {
      const gymsResult = await getManagerGyms();
      if (gymsResult.success && gymsResult.data) {
        companyGyms = gymsResult.data.map(gym => ({
          id: gym.id,
          name: gym.name,
          slug: gym.slug || '',
          city: gym.city || 'Bilinmeyen',
          district: gym.district || 'Bilinmeyen',
        }));
      }
    }

    const isGymManager = roles.includes('gym_manager');
    const isTrainer = roles.includes('trainer');
    let trainerGyms: GymData[] = [];
    let gymManagerGyms: GymData[] = [];

    const promises = [];
    if (isGymManager) {
      promises.push(getManagedGymsByManager());
    } else {
      promises.push(Promise.resolve({ success: true, data: [] })); // Eşleşme için boş promise
    }

    if (isTrainer) {
      promises.push(getTrainerGyms());
    } else {
      promises.push(Promise.resolve({ success: true, data: [] })); // Eşleşme için boş promise
    }

    const [managerGymsResponse, trainerGymsResponse] = await Promise.all(promises);

    if (managerGymsResponse.success && managerGymsResponse.data) {
      gymManagerGyms = managerGymsResponse.data.map((gym: any) => ({
        id: gym.id,
        name: gym.name,
        slug: gym.slug || '',
        city: gym.city || '',
        district: gym.district || '',
      }));
    }

    if (trainerGymsResponse.success && trainerGymsResponse.data) {
      trainerGyms = trainerGymsResponse.data.map((gym: any) => ({
        id: gym.gym_id,
        name: gym.gym_name,
        slug: '', // trainer gym'de slug yok
        city: gym.gym_city || '',
        district: gym.gym_district || '',
      }));
    }

    // Seçili bir salon için yetki bilgisini ekle (opsiyonel)
    const gymAccess = gymId ? await checkGymAccess(gymId) : undefined;

    return {
      authenticated: true,
      profile:
        profileResult.success && profileResult.data ? profileResult.data : null,
      roleOptions,
      companyGyms,
      trainerGyms,
      gymManagerGyms,
      isManager: roles.includes('company_manager') || roles.includes('gym_manager'),
      isTrainer: roles.includes('trainer'),
      isMember: roles.includes('member'),
      ...(gymId ? { gymAccess } : {}),
    };
  } catch (error) {
    console.error('Header auth status check failed:', error);
    return {
      authenticated: false,
      profile: null,
      roleOptions: [],
      companyGyms: [],
      trainerGyms: [],
      gymManagerGyms: [],
      isManager: false,
      isTrainer: false,
      isMember: false,
      ...(gymId ? { gymAccess: { hasAccess: false, role: 'none', gymId } } : {}),
    };
  }
};


