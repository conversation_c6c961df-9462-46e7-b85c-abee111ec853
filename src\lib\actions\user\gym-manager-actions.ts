'use server';

import { createAction } from '../core/core';
import { ApiResponse } from '@/types/global/api';
import { GymData } from '@/components/header/shared/header-types';
import { getSupabaseAdmin } from '@/lib/supabase/admin';

export async function checkGymManagerAccess(gymId: string): Promise<boolean> {
  const supabase = await getSupabaseAdmin();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return false;
  }

  const { data: gym, error } = await supabase
    .from('gyms')
    .select('id')
    .eq('id', gymId)
    .eq('manager_profile_id', user.id)
    .single();

  if (error || !gym) {
    return false;
  }

  return true;
}

/**
 * Bir gym_manager'ın yönettiği tüm salonları getirir.
 */
export async function getManagedGymsByManager(
  userId?: string | null
): Promise<ApiResponse<GymData[]>> {
  return await createAction<GymData[]>(async (_, supabase, authUserId) => {
    const targetUserId = userId || authUserId;

    if (!targetUserId) {
      throw new Error('Kullanıcı kimliği bulunamadı.');
    }

    const { data: gyms, error } = await supabase
      .from('gyms')
      .select('id, name, address, city, district, slug')
      .eq('manager_profile_id', targetUserId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Yöneticinin salonları getirilirken hata: ${error.message}`);
    }

    // Supabase'den gelen veriyi GymData formatına map'lemeye gerek yok, zaten uyumlu.
    return gyms || [];
  });
}
