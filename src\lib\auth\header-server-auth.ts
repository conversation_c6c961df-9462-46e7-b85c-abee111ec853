'use server';

import { headers } from 'next/headers';
import { getAuthenticatedUser, getUserRoles } from '@/lib/auth/server-auth';
import { getProfile } from '@/lib/actions/user/profile-actions';
import { getManagerGyms } from '@/lib/actions/dashboard/company/dashboard-actions';
import { getTrainerGyms } from '@/lib/actions/user/trainer-actions';
import { getManagedGymsByManager } from '@/lib/actions/user/gym-manager-actions';
import { createRoleOptions } from '@/components/header/shared/header-constants';
import {
  HeaderAuthStatus,
  GymData,
} from '@/components/header/shared/header-types';

/**
 * Server component'ler için pathname alma
 * Middleware'den gelen x-pathname header'ını okur
 */
export async function getServerPathname(): Promise<string> {
  const headersList = await headers();
  return headersList.get('x-pathname') || '/';
}

/**
 * Server component'ler için header auth durumu
 * Static rendering'i bozmadan auth kontrolü yapar
 */
export async function getServerHeaderAuthStatus(): Promise<HeaderAuthStatus> {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return {
        authenticated: false,
        profile: null,
        roleOptions: [],
        companyGyms: [],
        trainerGyms: [],
        gymManagerGyms: [],
        isManager: false,
        isTrainer: false,
        isMember: false,
      };
    }

    const [profileResult, roles] = await Promise.all([
      getProfile(),
      getUserRoles(),
    ]);

    let companyGyms: GymData[] = [];
    if (roles.includes('company_manager')) {
      const gymsResult = await getManagerGyms();
      if (gymsResult.success && gymsResult.data) {
        companyGyms = gymsResult.data.map(gym => ({
          id: gym.id,
          name: gym.name,
          slug: gym.slug || '',
          city: gym.city || '',
          district: gym.district || '',
        }));
      }
    }

    const isGymManager = roles.includes('gym_manager');
    const isTrainer = roles.includes('trainer');
    let trainerGyms: GymData[] = [];
    let gymManagerGyms: GymData[] = [];

    const promises = [];
    if (isGymManager) {
      promises.push(getManagedGymsByManager());
    } else {
      promises.push(Promise.resolve({ success: true, data: [] }));
    }

    if (isTrainer) {
      promises.push(getTrainerGyms());
    } else {
      promises.push(Promise.resolve({ success: true, data: [] }));
    }

    const [managerGymsResponse, trainerGymsResponse] = await Promise.all(promises);

    if (managerGymsResponse.success && managerGymsResponse.data) {
      gymManagerGyms = managerGymsResponse.data.map((gym: any) => ({
        id: gym.id,
        name: gym.name,
        slug: gym.slug || '',
        city: gym.city || '',
        district: gym.district || '',
      }));
    }

    if (trainerGymsResponse.success && trainerGymsResponse.data) {
      trainerGyms = trainerGymsResponse.data.map((gym: any) => ({
        id: gym.gym_id,
        name: gym.gym_name,
        slug: '',
        city: gym.gym_city || '',
        district: gym.gym_district || '',
      }));
    }

    return {
      authenticated: true,
      profile: profileResult.success && profileResult.data ? profileResult.data : null,
      roleOptions: createRoleOptions(roles),
      companyGyms,
      trainerGyms,
      gymManagerGyms,
      isManager: roles.includes('company_manager') || roles.includes('gym_manager'),
      isTrainer: roles.includes('trainer'),
      isMember: roles.includes('member'),
    };
  } catch (error) {
    console.error('Server header auth status check failed:', error);
    return {
      authenticated: false,
      profile: null,
      roleOptions: [],
      companyGyms: [],
      trainerGyms: [],
      gymManagerGyms: [],
      isManager: false,
      isTrainer: false,
      isMember: false,
    };
  }
}
